from retreival import get_today_date, qna_search, product_search
from tools import get_all_tools, extract_and_save_user_info, get_user_info
import os
from dotenv import load_dotenv

from langchain.agents import create_tool_calling_agent, AgentExecutor
from langchain.chat_models import init_chat_model
from langchain_core.runnables.history import RunnableWithMessageHistory
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_mongodb.chat_message_histories import MongoDBChatMessageHistory
load_dotenv(override=True)

# Verify OpenAI API key is loaded
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
if not OPENAI_API_KEY:
    raise ValueError("OPENAI_API_KEY not found in environment variables")

# Combine retrieval tools with LangMem memory tools
tools = [get_today_date, qna_search, product_search] + get_all_tools()

# Load ReAct prompt

# Initialize ChatOpenAI model with proper configuration
# model = ChatOpenAI(
#     model="gpt-4o-mini",  # Use model parameter instead of name
#     temperature=0.1,
#     api_key=OPENAI_API_KEY,
# )

model = init_chat_model(
    model="gpt-4o-mini",  # Use model parameter instead of name
    temperature=0.1,
    api_key=OPENAI_API_KEY,
)


# Create the prompt template with LangMem memory capabilities
prompt = ChatPromptTemplate.from_messages([
    ("system", """You are a helpful assistant with persistent memory and booking capabilities using LangMem.

MEMORY CAPABILITIES:
- You remember previous conversations with users across sessions (MongoDB chat history)
- You automatically save user information using LangMem memory tools
- You can search and retrieve user information for personalized responses
- Your memory is stored persistently using LangMem's memory store

AVAILABLE TOOLS:
1. product_search - Search for available courses/products
2. qna_search - Search for technical support and troubleshooting
3. get_today_date - Get current date/time
4. book_service - Book a service/course (automatically uses saved user info)
5. save_user_information - Manually save user contact details
6. get_user_profile - Get saved user information
7. search_bookings - Search through previous bookings
8. manage_memory - LangMem tool to create/update memories
9. search_memory - LangMem tool to search stored memories

TOOL USAGE LOGIC:
- For product inquiries → use product_search
- For technical issues → use qna_search
- For booking requests → use book_service (automatically retrieves saved user info)
- For date/time → use get_today_date
- For saving contact info → use save_user_information
- For viewing profile → use get_user_profile
- For booking history → use search_bookings

BOOKING PROCESS:
1. When user wants to book something, book_service automatically checks for saved user info
2. If missing name, email, or phone - ask for the missing information
3. Use save_user_information to store provided details
4. Use book_service with all required information

USER INFO DETECTION:
- When user provides personal info, automatically save it using manage_memory
- Save as: "User name: John Smith", "User email: <EMAIL>", "User phone: ************"
- Use search_memory to retrieve user information when needed

Examples:
- "what courses do you have?" → use product_search
- "I want to book the German course" → use book_service (auto-retrieves user info)
- "my name is John Smith" → use save_user_information
- "what's my profile?" → use get_user_profile
- "show my bookings" → use search_bookings"""),
    ("placeholder", "{chat_history}"),
    ("human", "{input}"),
    ("placeholder", "{agent_scratchpad}"),
])

# Create the agent with MongoDB memory
def get_session_history(session_id: str) -> MongoDBChatMessageHistory:
    """
    Get or create MongoDB chat message history for a session
    This function creates a new MongoDBChatMessageHistory instance for each session
    """
    return MongoDBChatMessageHistory(
        connection_string=os.getenv("MONGO_URI", "mongodb://localhost:27017/"),
        database_name="test_agent_db",
        collection_name="chat_history",
        session_id=session_id
    )

agent = create_tool_calling_agent(model, tools, prompt)
agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True)

# Wrap agent with MongoDB-backed chat history
agent_with_chat_history = RunnableWithMessageHistory(
    agent_executor,
    get_session_history,  # Function that returns MongoDB chat history
    input_messages_key="input",
    history_messages_key="chat_history",
)

print("🧠 Enhanced Chat with LangMem + MongoDB Memory")
print("=" * 60)
print("Available features:")
print("- Ask questions about products (will use Product Search)")
print("- Ask technical questions (will use QNA Search)")
print("- Book services/courses (requires name, email, phone)")
print("- Automatic user information detection and saving")
print("- Type 'profile' to see your saved information")
print("- Type 'exit' or 'quit' to stop")
print("- Your conversation history is stored in MongoDB!")
print("=" * 60)

# Use a default session ID for this test
session_id = "langmem-session-001"
print(f"📋 Session ID: {session_id}")

while True:
    try:
        user_input = input(f"\n[{session_id[-8:]}] User: ")

        if user_input.lower() in ["exit", "quit"]:
            print("👋 Exiting the chat. Your conversation and user info are saved!")
            break
        elif user_input.lower() == "profile":
            # Get user profile using the tool
            user_info = get_user_info()
            if any(user_info.values()):
                print(f"\n👤 Your Profile:")
                for key, value in user_info.items():
                    if value:
                        print(f"   {key.title()}: {value}")
            else:
                print("\n👤 No profile information saved yet.")
            continue

        # Automatically extract and save user information
        auto_save_result = extract_and_save_user_info(user_input)
        if auto_save_result:
            print(auto_save_result)

        # Execute the agent with the user input and session configuration
        print("🤔 Processing your request with LangMem memory...")
        response = agent_with_chat_history.invoke(
            {"input": user_input},
            config={"configurable": {"session_id": session_id}}
        )

        # Print the response from the agent
        print(f"\n🤖 Agent: {response['output']}")

    except KeyboardInterrupt:
        print("\n👋 Exiting the chat. Your conversation and user info are saved!")
        break
    except Exception as e:
        print(f"\n❌ Error occurred: {str(e)}")
        print("Please try again or type 'exit' to quit.")