"""
Simple LangMem Memory Tools - Clean & Minimal
No classes, no bloat - just functional memory management
"""

import re
from datetime import datetime
from typing import Dict, Optional
from langchain_core.tools import tool
from langmem import create_manage_memory_tool, create_search_memory_tool
from langgraph.store.memory import InMemoryStore

# Simple memory store setup
memory_store = InMemoryStore(
    index={"dims": 1536, "embed": "openai:text-embedding-3-small"}
)

# Create memory tools - one for user info, one for bookings
user_memory = create_manage_memory_tool(namespace=("users",), store=memory_store)
search_user_memory = create_search_memory_tool(namespace=("users",), store=memory_store)
booking_memory = create_manage_memory_tool(namespace=("bookings",), store=memory_store)
search_booking_memory = create_search_memory_tool(namespace=("bookings",), store=memory_store)

# Simple extraction patterns
EMAIL_PATTERN = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
PHONE_PATTERN = r'(?:\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})'
NAME_PATTERNS = [
    r'(?:my name is|i am|i\'m|call me)\s+([A-Za-z\s]+?)(?:\s|$|\.|\,)',
    r'name:\s*([A-Za-z\s]+?)(?:\s|$|\.|\,)'
]

def extract_and_save_user_info(text: str) -> str:
    """Auto-extract and save user info from text"""
    saved = []
    
    # Extract email
    emails = re.findall(EMAIL_PATTERN, text)
    if emails:
        user_memory.invoke({"content": f"User email: {emails[0]}"})
        saved.append(f"email: {emails[0]}")
    
    # Extract phone
    phones = re.findall(PHONE_PATTERN, text)
    if phones:
        phone = ''.join(phones[0])
        user_memory.invoke({"content": f"User phone: {phone}"})
        saved.append(f"phone: {phone}")
    
    # Extract name
    for pattern in NAME_PATTERNS:
        names = re.findall(pattern, text, re.IGNORECASE)
        if names:
            name = names[0].strip()
            if 1 < len(name) < 50:
                user_memory.invoke({"content": f"User name: {name}"})
                saved.append(f"name: {name}")
                break
    
    return f"💾 Saved: {', '.join(saved)}" if saved else ""

def get_user_info() -> Dict[str, Optional[str]]:
    """Get saved user info"""
    info = {"name": None, "email": None, "phone": None}
    
    try:
        for key in ["name", "email", "phone"]:
            result = search_user_memory.invoke({"query": f"user {key}"})
            if result and f"User {key}:" in result:
                match = re.search(rf'User {key}:\s*([^\n,]+)', result)
                if match:
                    info[key] = match.group(1).strip()
    except:
        pass
    
    return info

@tool
def save_user_info(name: str = "", email: str = "", phone: str = "") -> str:
    """Save user contact information"""
    saved = []
    
    if name:
        user_memory.invoke({"content": f"User name: {name}"})
        saved.append("name")
    if email:
        user_memory.invoke({"content": f"User email: {email}"})
        saved.append("email")
    if phone:
        user_memory.invoke({"content": f"User phone: {phone}"})
        saved.append("phone")
    
    if not saved:
        return "❌ No info provided"
    
    return f"✅ Saved {', '.join(saved)}"

@tool
def get_user_profile() -> str:
    """Get user profile"""
    info = get_user_info()
    
    if any(info.values()):
        profile = []
        for key, value in info.items():
            if value:
                profile.append(f"{key.title()}: {value}")
        return f"👤 Profile:\n" + "\n".join(f"• {p}" for p in profile)
    else:
        return "👤 No profile saved yet"

@tool
def book_service(service_name: str, user_name: str = "", user_email: str = "", user_phone: str = "") -> str:
    """Book a service - auto-uses saved user info"""
    # Get saved info if not provided
    saved_info = get_user_info()
    
    if not user_name and saved_info.get("name"):
        user_name = saved_info["name"]
    if not user_email and saved_info.get("email"):
        user_email = saved_info["email"]
    if not user_phone and saved_info.get("phone"):
        user_phone = saved_info["phone"]
    
    # Check required info
    missing = []
    if not user_name:
        missing.append("name")
    if not user_email:
        missing.append("email")
    if not user_phone:
        missing.append("phone")
    
    if missing:
        return f"❌ Missing: {', '.join(missing)}. Please provide to book."
    
    # Create booking
    booking_id = f"BK{datetime.now().strftime('%Y%m%d%H%M%S')}"
    booking_info = f"Booking {booking_id}: {service_name} for {user_name} ({user_email}, {user_phone}) on {datetime.now().strftime('%Y-%m-%d %H:%M')}"
    
    booking_memory.invoke({"content": booking_info})
    
    return f"""✅ Booking Confirmed!
    
📋 ID: {booking_id}
🎯 Service: {service_name}
👤 Customer: {user_name}
📧 Email: {user_email}
📱 Phone: {user_phone}
📅 Date: {datetime.now().strftime('%Y-%m-%d %H:%M')}

Thank you!"""

@tool
def search_bookings(query: str = "") -> str:
    """Search bookings"""
    if not query:
        query = "booking"
    
    try:
        results = search_booking_memory.invoke({"query": query})
        return f"📋 Bookings:\n{results}" if results else "📋 No bookings found"
    except Exception as e:
        return f"❌ Search failed: {str(e)}"

# Export tools
def get_all_tools():
    """Return all tools"""
    return [
        save_user_info,
        get_user_profile,
        book_service,
        search_bookings,
        user_memory,
        search_user_memory,
        booking_memory,
        search_booking_memory
    ]
