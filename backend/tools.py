"""
Dynamic Tools for LangMem Memory Management and Booking System
Simple functional approach without unnecessary classes
"""

import re
from datetime import datetime
from typing import Dict, Optional
from langchain_core.tools import tool
from langmem import create_manage_memory_tool, create_search_memory_tool
from langgraph.store.memory import InMemoryStore

# Initialize LangMem memory store
memory_store = InMemoryStore(
    index={
        "dims": 1536,
        "embed": "openai:text-embedding-3-small",
    }
)

# Create LangMem memory tools with different namespaces
user_memory_tool = create_manage_memory_tool(namespace=("user_info",), store=memory_store)
search_user_memory_tool = create_search_memory_tool(namespace=("user_info",), store=memory_store)
booking_memory_tool = create_manage_memory_tool(namespace=("bookings",), store=memory_store)
search_booking_memory_tool = create_search_memory_tool(namespace=("bookings",), store=memory_store)

# Define extraction patterns
EXTRACTION_PATTERNS = {
    "email": r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
    "phone": r'(?:\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})',
    "name": [
        r'(?:my name is|i am|i\'m|call me)\s+([A-Za-z\s]+?)(?:\s|$|\.|\,)',
        r'name:\s*([A-Za-z\s]+?)(?:\s|$|\.|\,)'
    ]
}

def extract_and_save_user_info(text: str) -> str:
    """Extract and save user information from text using LangMem"""
    saved_info = []

    # Extract email
    emails = re.findall(EXTRACTION_PATTERNS["email"], text)
    if emails:
        user_memory_tool.invoke({"content": f"User email: {emails[0]}"})
        saved_info.append(f"email: {emails[0]}")

    # Extract phone
    phones = re.findall(EXTRACTION_PATTERNS["phone"], text)
    if phones:
        phone = ''.join(phones[0])
        user_memory_tool.invoke({"content": f"User phone: {phone}"})
        saved_info.append(f"phone: {phone}")

    # Extract name
    for pattern in EXTRACTION_PATTERNS["name"]:
        names = re.findall(pattern, text, re.IGNORECASE)
        if names:
            name = names[0].strip()
            if 1 < len(name) < 50:  # Basic validation
                user_memory_tool.invoke({"content": f"User name: {name}"})
                saved_info.append(f"name: {name}")
                break

    return f"💾 Automatically saved: {', '.join(saved_info)}" if saved_info else ""

def get_user_info() -> Dict[str, Optional[str]]:
    """Get user information using LangMem search"""
    user_info = {"name": None, "email": None, "phone": None}

    try:
        # Search for each type of info
        for info_type in ["name", "email", "phone"]:
            result = search_user_memory_tool.invoke({"query": f"user {info_type}"})
            if result and f"User {info_type}:" in result:
                match = re.search(rf'User {info_type}:\s*([^\n,]+)', result)
                if match:
                    user_info[info_type] = match.group(1).strip()
    except Exception:
        pass

    return user_info


@tool
def save_user_information(name: str = "", email: str = "", phone: str = "") -> str:
    """
    Save user contact information for future use using LangMem.

    Args:
        name: User's full name
        email: User's email address
        phone: User's phone number
    """
    try:
        saved_items = []

        if name:
            user_memory_tool.invoke({"content": f"User name: {name}"})
            saved_items.append("name")
        if email:
            user_memory_tool.invoke({"content": f"User email: {email}"})
            saved_items.append("email")
        if phone:
            user_memory_tool.invoke({"content": f"User phone: {phone}"})
            saved_items.append("phone")

        if not saved_items:
            return "❌ No information provided to save"

        return f"✅ Saved user {', '.join(saved_items)}. I'll remember this information for future bookings!"

    except Exception as e:
        return f"❌ Failed to save user information: {str(e)}"


@tool
def book_service(service_name: str, user_name: str = "", user_email: str = "", user_phone: str = "") -> str:
    """
    Book a service/course for the user. Automatically uses saved user information if available.

    Args:
        service_name: Name of the service/course to book
        user_name: User's full name (optional if already saved)
        user_email: User's email address (optional if already saved)
        user_phone: User's phone number (optional if already saved)
    """
    try:
        # Get saved user information if not provided
        saved_info = get_user_info()

        # Use saved info if parameters not provided
        if not user_name and saved_info.get("name"):
            user_name = saved_info["name"]
        if not user_email and saved_info.get("email"):
            user_email = saved_info["email"]
        if not user_phone and saved_info.get("phone"):
            user_phone = saved_info["phone"]

        # Validate required information
        missing_info = []
        if not user_name:
            missing_info.append("name")
        if not user_email:
            missing_info.append("email")
        if not user_phone:
            missing_info.append("phone")

        if missing_info:
            return f"❌ Cannot complete booking. Missing required information: {', '.join(missing_info)}. Please provide your {', '.join(missing_info)} to proceed with booking."

        # Generate booking ID
        booking_id = f"BK{datetime.now().strftime('%Y%m%d%H%M%S')}"

        # Save booking to memory using LangMem
        booking_info = f"Booking confirmed - ID: {booking_id}, Service: {service_name}, Customer: {user_name}, Email: {user_email}, Phone: {user_phone}, Date: {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        booking_memory_tool.invoke({"content": booking_info})

        return f"""✅ Booking Confirmed!

📋 Booking Details:
• Booking ID: {booking_id}
• Service: {service_name}
• Customer: {user_name}
• Email: {user_email}
• Phone: {user_phone}
• Date: {datetime.now().strftime('%Y-%m-%d %H:%M')}

📧 A confirmation email will be sent to {user_email}
📱 You may receive an SMS confirmation at {user_phone}

Thank you for your booking!"""

    except Exception as e:
        return f"❌ Booking failed: {str(e)}"


@tool
def get_user_profile() -> str:
    """
    Get the current user's saved profile information.
    """
    try:
        user_info = get_user_info()

        profile_parts = []
        for key, value in user_info.items():
            if value:
                profile_parts.append(f"{key.title()}: {value}")

        if profile_parts:
            return f"👤 Your Profile:\n" + "\n".join(f"• {part}" for part in profile_parts)
        else:
            return "👤 No profile information saved yet. Please provide your name, email, and phone number."

    except Exception as e:
        return f"❌ Failed to retrieve profile: {str(e)}"


@tool
def search_bookings(query: str = "") -> str:
    """
    Search through previous bookings.

    Args:
        query: Search query for bookings (optional)
    """
    try:
        if not query:
            query = "booking confirmed"

        results = search_booking_memory_tool.invoke({"query": query})

        if results:
            return f"📋 Found bookings:\n{results}"
        else:
            return "📋 No bookings found."

    except Exception as e:
        return f"❌ Failed to search bookings: {str(e)}"


# Export all tools for use in the main application
def get_all_tools():
    """Return all available tools"""
    return [
        save_user_information,
        book_service,
        get_user_profile,
        search_bookings,
        user_memory_tool,
        search_user_memory_tool,
        booking_memory_tool,
        search_booking_memory_tool
    ]
