"""
Dynamic LangMem Memory Tools with LLM-powered structured data extraction
Clean, flexible, and intelligent user info and booking management
"""

import json
from datetime import datetime
from typing import Dict, Optional, Any
from langchain_core.tools import tool
from langchain_core.prompts import ChatPromptTemplate
from langchain.chat_models import init_chat_model
from langmem import create_manage_memory_tool, create_search_memory_tool
from langgraph.store.memory import InMemoryStore
import os
from dotenv import load_dotenv

load_dotenv()

# Simple memory store setup
memory_store = InMemoryStore(
    index={"dims": 1536, "embed": "openai:text-embedding-3-small"}
)

# Create memory tools
user_memory = create_manage_memory_tool(namespace=("users",), store=memory_store)
search_user_memory = create_search_memory_tool(namespace=("users",), store=memory_store)
booking_memory = create_manage_memory_tool(namespace=("bookings",), store=memory_store)
search_booking_memory = create_search_memory_tool(namespace=("bookings",), store=memory_store)

# LLM for structured extraction
llm = init_chat_model(
    model="gpt-4o-mini",
    temperature=0.1,
    api_key=os.getenv("OPENAI_API_KEY"),
)

# LLM-powered extraction prompts
EXTRACT_USER_INFO_PROMPT = ChatPromptTemplate.from_messages([
    ("system", """Extract user information from the text. Return ONLY a JSON object with these fields:
    - name: full name (null if not found)
    - email: email address (null if not found)
    - phone: 10-digit phone number starting with 9 (null if not found or invalid)

    Example: {"name": "John Doe", "email": "<EMAIL>", "phone": "9841234567"}
    Return {"name": null, "email": null, "phone": null} if nothing found."""),
    ("human", "Text: {text}")
])

def extract_user_info_with_llm(text: str) -> Dict[str, Optional[str]]:
    """Use LLM to extract structured user info"""
    try:
        prompt = EXTRACT_USER_INFO_PROMPT.format(text=text)
        response = llm.invoke(prompt)

        # Parse JSON response
        info = json.loads(response.content)

        # Validate phone format
        if info.get("phone"):
            phone = str(info["phone"]).replace(" ", "").replace("-", "")
            if len(phone) != 10 or not phone.startswith('9') or not phone.isdigit():
                info["phone"] = None

        return info
    except:
        return {"name": None, "email": None, "phone": None}

def extract_and_save_user_info(text: str) -> str:
    """Auto-extract and save user info using LLM"""
    info = extract_user_info_with_llm(text)
    saved = []

    for key, value in info.items():
        if value:
            user_memory.invoke({"content": f"User {key}: {value}"})
            saved.append(f"{key}: {value}")

    return f"💾 Saved: {', '.join(saved)}" if saved else ""

def get_user_info() -> Dict[str, Optional[str]]:
    """Get saved user info using LLM search"""
    try:
        # Search for user information
        result = search_user_memory.invoke({"query": "user name email phone contact information"})

        if result:
            # Use LLM to extract structured data from search results
            extract_prompt = ChatPromptTemplate.from_messages([
                ("system", """Extract user information from the search results. Return ONLY a JSON object:
                {"name": "value or null", "email": "value or null", "phone": "value or null"}"""),
                ("human", "Search results: {results}")
            ])

            prompt = extract_prompt.format(results=result)
            response = llm.invoke(prompt)
            return json.loads(response.content)
    except:
        pass

    return {"name": None, "email": None, "phone": None}

@tool
def save_user_info(**kwargs) -> str:
    """Save user contact information dynamically"""
    if not kwargs:
        return "❌ No information provided"

    saved = []
    errors = []

    # Simple validation without LLM complexity
    for key, value in kwargs.items():
        if not value or not str(value).strip():
            continue

        value = str(value).strip()

        if key == "name" and len(value) >= 2:
            user_memory.invoke({"content": f"User name: {value}"})
            saved.append("name")
        elif key == "email" and "@" in value and "." in value:
            user_memory.invoke({"content": f"User email: {value}"})
            saved.append("email")
        elif key == "phone":
            # Clean phone number
            clean_phone = ''.join(c for c in value if c.isdigit())
            if len(clean_phone) == 10 and clean_phone.startswith('9'):
                user_memory.invoke({"content": f"User phone: {clean_phone}"})
                saved.append("phone")
            else:
                errors.append("phone must be 10 digits starting with 9")
        elif key in ["name", "email", "phone"]:
            errors.append(f"invalid {key} format")

    # Build response
    response_parts = []
    if saved:
        response_parts.append(f"✅ Saved {', '.join(saved)}")
    if errors:
        response_parts.append(f"❌ Errors: {', '.join(errors)}")

    return " | ".join(response_parts) if response_parts else "❌ No valid info provided"

@tool
def get_user_profile() -> str:
    """Get user profile"""
    info = get_user_info()
    
    if any(info.values()):
        profile = []
        for key, value in info.items():
            if value:
                profile.append(f"{key.title()}: {value}")
        return f"👤 Profile:\n" + "\n".join(f"• {p}" for p in profile)
    else:
        return "👤 No profile saved yet"

@tool
def book_service(service_name: str, **kwargs) -> str:
    """Book a service with auto-retrieval of saved user info"""
    # Get saved info
    saved_info = get_user_info()

    # Use provided info or fall back to saved info
    user_name = kwargs.get("user_name", "") or saved_info.get("name", "")
    user_email = kwargs.get("user_email", "") or saved_info.get("email", "")
    user_phone = kwargs.get("user_phone", "") or saved_info.get("phone", "")

    # Check required fields
    missing = []
    if not user_name or len(user_name.strip()) < 2:
        missing.append("name")
    if not user_email or "@" not in user_email or "." not in user_email:
        missing.append("email")
    if not user_phone:
        missing.append("phone")
    else:
        # Clean and validate phone
        clean_phone = ''.join(c for c in user_phone if c.isdigit())
        if len(clean_phone) != 10 or not clean_phone.startswith('9'):
            missing.append("valid phone (10 digits starting with 9)")
        else:
            user_phone = clean_phone

    if missing:
        return f"❌ Missing: {', '.join(missing)}. Please provide to complete booking."

    # Create booking
    booking_id = f"BK{datetime.now().strftime('%Y%m%d%H%M%S')}"
    booking_info = {
        "booking_id": booking_id,
        "service": service_name,
        "customer": user_name,
        "email": user_email,
        "phone": user_phone,
        "date": datetime.now().strftime('%Y-%m-%d %H:%M')
    }

    # Save booking
    booking_memory.invoke({"content": f"Booking: {json.dumps(booking_info)}"})

    return f"""✅ Booking Confirmed!

📋 ID: {booking_id}
🎯 Service: {service_name}
👤 Customer: {user_name}
📧 Email: {user_email}
📱 Phone: {user_phone}
📅 Date: {booking_info['date']}

Thank you!"""

@tool
def search_bookings(query: str = "") -> str:
    """Search bookings with LLM-powered formatting"""
    try:
        search_query = query if query else "booking confirmed"
        results = search_booking_memory.invoke({"query": search_query})

        if not results:
            return "📋 No bookings found"

        # Use LLM to format booking results nicely
        format_prompt = ChatPromptTemplate.from_messages([
            ("system", """Format booking search results in a clean, readable way.
            Extract key information like booking ID, service, customer, date.
            Present as a numbered list with clear formatting."""),
            ("human", "Raw booking data: {results}")
        ])

        prompt = format_prompt.format(results=results)
        response = llm.invoke(prompt)

        return f"📋 Found Bookings:\n{response.content}"

    except Exception as e:
        return f"❌ Search failed: {str(e)}"

# Export tools
def get_all_tools():
    """Return all tools"""
    return [
        save_user_info,
        get_user_profile,
        book_service,
        search_bookings,
        user_memory,
        search_user_memory,
        booking_memory,
        search_booking_memory
    ]
